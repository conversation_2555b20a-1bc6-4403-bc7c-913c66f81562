!pip install -qU langchain langchain-openai langchain-community langchain-core
# !pip install -qU duckduckgo-search wikipedia
# !pip install -qU pydantic

import os
# Set up OpenAI API key
if not os.getenv("OPENAI_API_KEY"):
    os.environ["OPENAI_API_KEY"] = ""

os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_API_KEY"] = ""
os.environ["LANGCHAIN_PROJECT"] = "langgraph-yt"

from langchain_openai import ChatOpenAI

# Initialize the LLM
llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0)

# Basic invocation
response = llm.invoke("What is the capital of France?")
print(response.content)

!pip install -qU duckduckgo-search wikipedia

from langchain_community.tools import DuckDuckGoSearchRun
from langchain_community.tools import WikipediaQueryRun
from langchain_community.utilities import WikipediaAPIWrapper

# Initialize tools
search = DuckDuckGoSearchRun()
wikipedia = WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())

tools = [search, wikipedia]

# Bind tools to LLM
llm_with_tools = llm.bind_tools(tools)

# Test with tools
response = llm_with_tools.invoke("Search for information about LangChain")
print(response)

!pip install -qU pydantic

from pydantic import BaseModel, Field
from typing import List

class PersonInfo(BaseModel):
    """Information about a person."""
    name: str = Field(description="The person's name")
    age: int = Field(description="The person's age")
    occupation: str = Field(description="The person's occupation")
    skills: List[str] = Field(description="List of skills")

# Create structured LLM
structured_llm = llm.with_structured_output(PersonInfo)

# Test structured output
response = structured_llm.invoke("Tell me about a software engineer named John who is 30 years old")
print(response)
print(type(response))

!pip install -qU langgraph

from langgraph.graph import StateGraph, END
from typing import TypedDict, Annotated
from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
import operator

# Define the state
class State(TypedDict):
    messages: Annotated[list[BaseMessage], operator.add]

# Define the chatbot node
def chatbot(state: State):
    return {"messages": [llm.invoke(state["messages"])]}

# Create the graph
graph_builder = StateGraph(State)
graph_builder.add_node("chatbot", chatbot)
graph_builder.set_entry_point("chatbot")
graph_builder.add_edge("chatbot", END)

graph = graph_builder.compile()

# Test the chatbot
result = graph.invoke({"messages": [HumanMessage(content="Hello! How are you?")]})
print(result["messages"][-1].content)

from langgraph.checkpoint.memory import MemorySaver

# Create memory saver
memory = MemorySaver()

# Compile graph with memory
graph_with_memory = graph_builder.compile(checkpointer=memory)

# Test with memory
config = {"configurable": {"thread_id": "1"}}

# First message
result1 = graph_with_memory.invoke(
    {"messages": [HumanMessage(content="Hi, my name is Alice")]}, 
    config
)
print("First response:", result1["messages"][-1].content)

# Second message (should remember Alice)
result2 = graph_with_memory.invoke(
    {"messages": [HumanMessage(content="What's my name?")]}, 
    config
)
print("Second response:", result2["messages"][-1].content)

from langgraph.prebuilt import create_react_agent

# Create agent with tools
agent = create_react_agent(llm, tools)

# Test the agent
response = agent.invoke({"messages": [HumanMessage(content="Search for the latest news about AI")]})
print(response["messages"][-1].content)

!pip install -qU langchain-chroma sentence-transformers

from langchain_community.document_loaders import TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_chroma import Chroma
from langchain_core.tools import tool
from langchain_core.documents import Document

# Sample documents for RAG
documents = [
    Document(page_content="GreenGrow Innovations was founded in 2010 by Dr. Sarah Chen and Mark Rodriguez. The company specializes in sustainable agriculture technology."),
    Document(page_content="GreenGrow's flagship product is the SmartFarm system, which uses IoT sensors and AI to optimize crop yields while reducing water usage by up to 40%."),
    Document(page_content="In 2023, GreenGrow Innovations raised $50 million in Series B funding led by EcoVentures Capital to expand their operations globally."),
    Document(page_content="The company's headquarters is located in Austin, Texas, with additional offices in California and Netherlands.")
]

# Split documents
text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
splits = text_splitter.split_documents(documents)

# Create embeddings and vector store
embeddings = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
vectorstore = Chroma.from_documents(documents=splits, embedding=embeddings)
retriever = vectorstore.as_retriever()

# Create RAG tool
@tool
def retrieve_documents(query: str) -> str:
    """Retrieve relevant documents from the knowledge base."""
    docs = retriever.invoke(query)
    return "\n\n".join([doc.page_content for doc in docs])

# Add RAG tool to tools list
rag_tools = [retrieve_documents] + tools

# Create RAG agent
rag_agent = create_react_agent(llm, rag_tools, checkpointer=memory)

# Test RAG agent
config = {"configurable": {"thread_id": "rag_session"}}

response = rag_agent.invoke(
    {"messages": [HumanMessage(content="Tell me about GreenGrow Innovations")]}, 
    config
)
print("RAG Agent Response:")
print(response["messages"][-1].content)

# Interactive RAG Agent CLI
def run_rag_agent():
    print("RAG Agent CLI (type 'quit' or 'exit' to stop)")
    print("-" * 50)
    
    config = {"configurable": {"thread_id": "interactive_session"}}
    
    while True:
        try:
            user_input = input("\nYou: ").strip()
            if user_input.lower() in {"quit", "exit"}:
                break
            
            if not user_input:
                continue
            
            # Get response from RAG agent
            result = rag_agent.invoke(
                {"messages": [HumanMessage(content=user_input)]}, 
                config
            )
            
            # Extract the last AI message
            last_message = next((m for m in reversed(result["messages"])
                               if isinstance(m, AIMessage)), None)
            
            if last_message:
                print(f"Agent: {last_message.content}")
            else:
                print("Agent: No response generated")
        
        except Exception as e:
            print(f"Error: {e}")
    
    print("\nGoodbye!")

# Uncomment to run interactive demo
# run_rag_agent()

# Visualize the agent graph
try:
    img = rag_agent.get_graph(xray=True).draw_mermaid_png()
    with open("rag_agent_graph.png", "wb") as f:
        f.write(img)
    
    from IPython.display import Image, display
    display(Image("rag_agent_graph.png"))
except Exception as e:
    print(f"Could not generate graph visualization: {e}")
    print("Graph structure:")
    print(rag_agent.get_graph().draw_ascii())