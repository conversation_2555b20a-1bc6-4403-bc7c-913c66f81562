# 🔹 LangGraph RAG Agent Tutorial

This repository contains a comprehensive Ju<PERSON>ter notebook that walks you through building a sophisticated RAG (Retrieval-Augmented Generation) Agent using LangGraph. The tutorial progresses from basic LLM concepts to advanced agent implementations.

## 📚 Topics Covered in Detail

### 1. **LangChain LLM Basics**
- **What it is**: Foundation of working with Large Language Models using LangChain
- **Key Concepts**:
  - LLM initialization and configuration
  - Basic invocation patterns
  - Temperature settings for controlling randomness
  - Model selection (GPT-3.5-turbo, GPT-4, etc.)
- **Why it matters**: Understanding LLM basics is crucial for building any AI application
- **Example**: Simple question-answering with ChatOpenAI

### 2. **LLM Invocation**
- **What it is**: Different methods to interact with LLMs programmatically
- **Key Concepts**:
  - Synchronous vs asynchronous invocation
  - Batch processing
  - Streaming responses
  - Error handling and retries
- **Practical Applications**: 
  - Single queries for immediate responses
  - Batch processing for multiple questions
  - Real-time chat applications

### 3. **LLM with Tools**
- **What it is**: Extending LLM capabilities by integrating external tools and APIs
- **Key Concepts**:
  - Tool binding and integration
  - Function calling capabilities
  - Tool selection and routing
  - External API integration
- **Tools Demonstrated**:
  - **DuckDuckGo Search**: Web search capabilities
  - **Wikipedia**: Knowledge base queries
  - Custom tools for specific domains
- **Why it's powerful**: Allows LLMs to access real-time information and perform actions beyond their training data

### 4. **Structured Output from LLM**
- **What it is**: Forcing LLMs to return data in specific, predictable formats
- **Key Concepts**:
  - Pydantic models for data validation
  - Schema enforcement
  - Type safety and validation
  - JSON schema generation
- **Benefits**:
  - Reliable data parsing
  - Integration with downstream systems
  - Reduced error handling complexity
- **Example**: PersonInfo model with name, age, occupation, and skills fields

### 5. **Basic LangGraph Chatbot**
- **What it is**: Creating stateful conversational agents using LangGraph's graph-based approach
- **Key Concepts**:
  - State management with TypedDict
  - Graph nodes and edges
  - Message flow control
  - Conversation continuity
- **Architecture**:
  - State definition for message history
  - Chatbot node for processing
  - Graph compilation and execution
- **Advantages**: More control over conversation flow compared to simple chains

### 6. **Adding Memory to the Chatbot**
- **What it is**: Implementing persistent memory to maintain context across conversations
- **Key Concepts**:
  - MemorySaver for state persistence
  - Thread-based conversation tracking
  - Checkpointing mechanisms
  - Session management
- **Implementation Details**:
  - Thread IDs for user session isolation
  - Automatic state saving and loading
  - Memory retrieval for context
- **Real-world Applications**: Customer service bots, personal assistants, educational tutors

### 7. **LangGraph Agent with Tools**
- **What it is**: Combining the power of agents with external tools for complex task execution
- **Key Concepts**:
  - ReAct (Reasoning + Acting) pattern
  - Tool selection and execution
  - Multi-step reasoning
  - Error recovery and retry logic
- **Agent Capabilities**:
  - Autonomous tool selection
  - Chain of thought reasoning
  - Dynamic planning and execution
  - Result synthesis
- **Use Cases**: Research assistants, data analysis, automated workflows

### 8. **LangGraph RAG Agent**
- **What it is**: The culmination - a sophisticated agent that combines retrieval and generation
- **Key Components**:
  - **Document Processing**: Text splitting and chunking
  - **Embeddings**: Converting text to vector representations
  - **Vector Store**: Chroma database for similarity search
  - **Retrieval Tool**: Custom tool for document retrieval
  - **Agent Integration**: Combining retrieval with reasoning
- **Technical Implementation**:
  - HuggingFace embeddings (all-MiniLM-L6-v2)
  - Chroma vector database
  - Recursive character text splitter
  - Custom retrieval tool with @tool decorator
- **Knowledge Base**: Sample company data about "GreenGrow Innovations"

## 🛠️ Technical Architecture

### RAG Pipeline Flow:
1. **Document Ingestion**: Load and process documents
2. **Text Splitting**: Break documents into manageable chunks
3. **Embedding Generation**: Convert text to vectors
4. **Vector Storage**: Store embeddings in Chroma DB
5. **Query Processing**: User question → embedding → similarity search
6. **Context Retrieval**: Fetch relevant document chunks
7. **Response Generation**: LLM generates answer using retrieved context
8. **Tool Integration**: Combine with web search and other tools

### Agent Decision Making:
1. **Query Analysis**: Understand user intent
2. **Tool Selection**: Choose appropriate tools (RAG, search, Wikipedia)
3. **Information Gathering**: Execute selected tools
4. **Synthesis**: Combine information from multiple sources
5. **Response Generation**: Provide comprehensive answer

## 🚀 Getting Started

### Prerequisites
```bash
pip install langchain langchain-openai langchain-community langchain-core
pip install langgraph langchain-chroma sentence-transformers
pip install duckduckgo-search wikipedia pydantic
```

### Environment Setup
```python
import os
os.environ["OPENAI_API_KEY"] = "your-api-key-here"
os.environ["LANGCHAIN_TRACING_V2"] = "true"  # Optional: for debugging
os.environ["LANGCHAIN_API_KEY"] = "your-langsmith-key"  # Optional
```

### Running the Notebook
1. Open `RAG_AI_Agent_using_LangGraph.ipynb` in Jupyter
2. Run cells sequentially to build understanding
3. Experiment with the interactive demo
4. Modify the knowledge base for your domain

## 🎯 Key Learning Outcomes

After completing this tutorial, you'll understand:

1. **Foundation Skills**:
   - How to work with LLMs programmatically
   - Tool integration patterns
   - Structured data handling

2. **Advanced Concepts**:
   - Graph-based agent architecture
   - Memory and state management
   - RAG implementation from scratch

3. **Practical Applications**:
   - Building domain-specific chatbots
   - Creating knowledge-based assistants
   - Implementing multi-tool agents

4. **Production Considerations**:
   - Error handling and recovery
   - Performance optimization
   - Scalability patterns

## 🔧 Customization Options

### Adding Your Own Knowledge Base:
- Replace sample documents with your domain data
- Adjust chunk size and overlap for your content
- Experiment with different embedding models

### Tool Extensions:
- Add custom APIs and databases
- Integrate with your existing systems
- Create domain-specific tools

### Agent Behavior:
- Modify prompts for different personalities
- Adjust reasoning patterns
- Implement custom decision logic

## 📈 Next Steps

1. **Experiment** with different LLM models
2. **Expand** the knowledge base with your data
3. **Add** more sophisticated tools
4. **Deploy** to production environments
5. **Monitor** and optimize performance

This tutorial provides a solid foundation for building production-ready RAG agents that can handle complex, multi-step queries while maintaining context and leveraging multiple information sources.
